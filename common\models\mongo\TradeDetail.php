<?php

namespace common\models\mongo;

use Yii;
use yii\mongodb\ActiveRecord;

class TradeDetail extends ActiveRecord
{
    public static function collectionName()
    {
        return 'trade_detail';
    }

    public function attributes()
    {
        return [
            '_id',
            'code',
            'qty',
            'amount',
            'payment',
            'approve',
            'cod',
            'cancle',  // 订单是否被取消 true 支付失败|没有支付  false 支付成功
            'msg',
            'vipIdCard',
            'vipEmail',
            'vipRealName',
            'accountStatus',
            'accountAmount',
            'assignState',
            'refund',
            'wms',
            'platform_code',
            'createtime',
            'modifytime',
            'dealtime',
            'paytime',
            'shop_name',
            'shop_code',
            'warehouse_name',
            'warehouse_code',
            'express_name',
            'express_code',
            'vip_name',
            'vip_code',
            'receiver_name',
            'receiver_phone',
            'receiver_mobile',
            'receiver_zip',
            'receiver_address',
            'receiver_area',
            'buyer_memo',
            'seller_memo',
            'seller_memo_late',
            'post_fee',
            'cod_fee',
            'discount_fee',
            'post_cost',
            'weight_origin',
            'receiver_name',
            'payment_amount',
            'delivery_state',
            'order_type_name',
            'express_no',
            'business_man',
            'create_name',
            'unique_tid',
            'hold_info',
            'platform_flag',
            'error_msg',
            'extend_memo',
            'tax_amount',
            'delivery_statusInfo',
            'approveDate',
            'accountDate',
            'trade_tag_code',
            'trade_tag_name',
            'plan_delivery_date',
            'details',
            'payments',
            'invoices',
            'deliverys',
            'tags',
            'messages',
            'platform_trading_state',
            'system_trading_state_desc',
            "substitut_order",
            "other_service_fee",
            "drp_tenant_name",
            "tariff_total",
            "refund_fee",
            "trade_print",
            "currency_code",
            "currency_name",
            "from_type_name",
            "humen_express_code",
            "humen_express_name",
            "pre_sale",
            "sub_type_name",
            "distribution_order",
            "distribution_post_fee",
            "timeliness_type_name",
            "estimated_arrival_time",
            "store_name",
            "refund_state",
            "source_type",
            "extend_info",
            "drp_customer_name",
            "confirm_receipt_time",
            "offline_memo",
            "from_type",
            "tracking_number",
            "wms_order",
            "approve_strategy_level"

        ];
    }

    public static function getDb()
    {
        return Yii::$app->get('mongo_gy');
    }

    public static function getByCode($code)
    {
        return self::find()->where(['code' => $code])->select(['amount','cancle', 'delivery_state', 'post_cost', 'express_code', 'express_name', 'deliverys', 'warehouse_code', 'warehouse_name','extend_info'])->asArray()->one();
    }

    public static function getLastestOne($shop_code, $goods_id, $sku_code)
    {
        return self::find()->select(['details'])->where(['shop_code' => $shop_code, 'details.cancel' => false, 'details.refund' => 0, 'details.platform_item_id' => $goods_id, 'details.platform_sku' => $sku_code])
            ->orderBy('paytime desc')->limit(1)->one();
    }

    public static function getLastestAmount($shop_code, $goods_id, $sku_code)
    {
        $amount = 0;
        $one = self::getLastestOne($shop_code, $goods_id, $sku_code);
        if (!$one) return $amount;
        foreach ($one->details as $detail) {
            if ($detail['platform_sku'] == $sku_code && $detail['platform_item_id'] == $goods_id) {
                $amount = bcadd($amount, $detail['amount_after'], 2);
            }
        }
        return (float)$amount;
    }
}
